# MRNN & Transformer Removal – Step-by-Step Plan

> Objective: Cleanly eliminate MRNN and Transformer-based models from the project, simplify the pipeline, and ensure no dangling references remain.
>
> Scope covers source code, tests, configuration, documentation, and dependency lists.
>
> **EXCLUSION CLAUSE**: SAITS, BRITS, autoencoder, and UNet models are explicitly EXCLUDED from this removal process and must be preserved throughout all operations.

---

## 0. Model Preservation Declaration

| Models to PRESERVE | Rationale |
|-------------------|-----------|
| **SAITS** | State-of-the-art imputation model - keep operational |
| **BRITS** | Bidirectional RNN for time series - critical functionality |
| **Autoencoder** | Core feature extraction model - maintain availability |
| **UNet** | Advanced neural network architecture - preserve for production |

**Preservation Checklist**: All removal operations must verify these models remain functional and accessible post-cleanup.

---

## 1. Inventory & Impact Analysis

| Area | Action |
|------|--------|
| **Models** | List files implementing or importing MRNN or Transformer logic (`models/advanced_models/mrnn_model.py`, `models/advanced_models/transformer_*`, etc.). |
| **Pipelines** | Search for `"mrnn"` / `"transformer"` keywords across `ml_core*.py`, `utils/`, training scripts, and notebooks. |
| **Configs** | Identify config files or CLI flags exposing these models (e.g., `config/*.ini`, `argument parsers`). |
| **Tests** | Locate tests using MRNN/Transformer (look into `archives/test_*`, `tests/`, and `archives/gpu_process`). |
| **Docs** | Note documentation sections referencing the removed models. |

Deliverable: **Impact Matrix** summarizing files to delete/modify.

---

## 1.5. Import Dependency Analysis

| Area | Action |
|------|--------|
| **Circular Imports** | Map import chains involving MRNN/Transformer to identify potential circular dependencies. |
| **Deep Integration** | Analyze `ml_core.py`, `ml_core_phase1_integration.py` for embedded MRNN/Transformer logic. |
| **Utility Dependencies** | Check `utils/` modules (GPU acceleration, optimization, memory management) for model-specific code. |
| **Framework Integration** | Verify PyPOTS, CatBoost, and other framework integrations don't rely on removed models. |

Deliverable: **Dependency Graph** showing safe removal order.

---

## 2. Deprecate Public Interfaces

1. **Mark models as deprecated** (single commit):
   - Add a deprecation notice at top of each MRNN/Transformer file.
   - Emit `warnings.warn("MRNN model deprecated – slated for removal")` when imported.
2. Update CLI or configuration defaults to hide these options while maintaining backward compatibility for one release cycle (if required).

---

## 3. Remove Source Files

1. Delete MRNN/Transformer implementation modules listed in Step 1.
2. Prune associated utility helpers (e.g., `models/advanced_models/utils/*` used exclusively by MRNN/Transformer).
3. Remove model registrations in `models/__init__.py` or factory patterns.

Commit message template:
```
chore: remove MRNN & Transformer implementations
```

---

## 3.5. Archive Directory Cleanup

1. **Remove MRNN-related archives**:
   - Delete `archives/test_files/test_mrnn_*.py` and related test files.
   - Clean up `archives/gpu_process/` MRNN-specific profiling and test files.
   - Remove failed fix attempts in `archives/fix/` related to MRNN/Transformer.
2. **Preserve valuable debugging utilities** that are model-agnostic.
3. **Update archive documentation** to reflect removed components.

Deliverable: **Archive Cleanup Report** documenting preserved vs. removed files.

---

## 4. Refactor Core Pipeline

1. **ml_core.py / training scripts**
   - Delete branching logic selecting MRNN/Transformer.
   - Simplify enum or dict mapping of available models.
2. **Prediction & evaluation utilities** – ensure no hard-coded fallbacks.
3. Adjust any pipeline diagrams / comments.

---

## 5. Update Configurations & CLI

- Remove config sections (INI/JSON/YAML) related to removed models.
- Drop CLI flags (`--model mrnn`, `--model transformer`).
- Validate default flows still operate (e.g., autoencoder, UNet, catboost).

---

## 5.5. Configuration Migration & Validation

1. **Config file analysis**:
   - Review `config/display_config.ini` for MRNN/Transformer-specific settings.
   - Check `config_handler.py` for model-specific configuration logic.
2. **Output format compatibility**:
   - Verify `reporting.py` handles remaining models correctly.
   - Test visualization utilities with reduced model set.
3. **Default configuration updates**:
   - Ensure default model selections point to available models.
   - Update configuration examples and templates.

Deliverable: **Configuration Migration Report** with before/after settings.

---

## 6. Dependency Cleanup

1. Check `requirements.txt` for libraries solely used by MRNN/Transformer (e.g., `pytorch-lightning`, specific transformer libs). Remove or downgrade if safe.
2. Run `pip-tools` or `pipdeptree` to verify no orphaned deps.

---

## 7. Test Suite Pruning & Refactor

1. Delete tests exclusively covering MRNN/Transformer (`archives/test_mrnn_*`, `test_transformer_init.py`).
2. Update integration tests to exclude removed model options.
3. Ensure coverage remains > X %.

---

## 8. Documentation & Examples

- Remove corresponding sections in `docs/` and README badges/examples.
- Update diagrams/screenshots.

---

## 9. Continuous Integration

- Edit CI matrix to drop jobs related to MRNN/Transformer configurations.
- Confirm pipelines pass.

---

## 10. Validation & Regression Testing

1. Run full test suite.
2. Execute core training/inference examples (autoencoder, UNet).
3. Benchmark memory/latency to confirm improvement or neutrality.

Deliverable: **Removal Verification Report** documenting results.

---

## 10.5. Data Pipeline Validation

1. **Well log data compatibility**:
   - Test data loading from `Las/` directory with remaining models.
   - Verify preprocessing pipelines work without MRNN/Transformer-specific logic.
2. **Data format validation**:
   - Ensure `data_handler.py` processes data correctly for remaining models.
   - Test `enhanced_preprocessing.py` functionality.
3. **Pipeline integrity**:
   - Validate end-to-end data flow from raw input to model predictions.

Deliverable: **Data Pipeline Validation Report**.

---

## 10.6. Framework Compatibility Verification

1. **PyPOTS integration**:
   - Test `example/PyPOTS_Quick_Start.ipynb` and tutorial scripts.
   - Verify SAITS and BRITS models still function correctly.
2. **CatBoost compatibility**:
   - Ensure CatBoost training and inference remain functional.
   - Test gradient boosting workflows.
3. **Multi-framework coordination**:
   - Verify model ensemble capabilities with remaining models.

Deliverable: **Framework Compatibility Report**.

---

## 10.7. Performance Regression Testing

1. **Memory optimization validation**:
   - Run `test_memory_optimization.py` and compare before/after metrics.
   - Test `utils/memory_optimization.py` with reduced model set.
2. **GPU acceleration verification**:
   - Validate `utils/gpu_acceleration.py` and `utils/gpu_fallback.py`.
   - Test CUDA operations with remaining models.
3. **Performance benchmarking**:
   - Execute `tests/performance_benchmarking.py`.
   - Compare training/inference speeds and resource usage.
4. **Gradient diagnostics**:
   - Run `gradient_diagnostics.py` to ensure numerical stability.

Deliverable: **Performance Regression Report** with detailed metrics.

---

## 11. Final Cleanup

- Run `pre-commit`, `black`, `isort`, and linter.
- Regenerate API docs if applicable.
- Tag release `vX.Y.0` removing MRNN/Transformer.

---

## 12. Risk Mitigation & Rollback

- Maintain a branch `legacy-mrnn-transformer` for archival.
- Document rollback procedure.

---

## Timeline Overview

| Week | Task |
|------|------|
| 1 | Steps 1–2 (Analysis, Dependencies & Deprecation) |
| 2 | Steps 3–4 (Code removal, Archive cleanup & Core refactor) |
| 3 | Steps 5–7 (Config migration, Dependencies, Tests) |
| 4 | Steps 8–10.7 (Docs, CI, Comprehensive validation) |
| 5 | Steps 11–12 (Final cleanup & Release) |

---

**Next Action**: Execute Step 1 to generate the detailed Impact Matrix.