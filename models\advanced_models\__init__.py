"""
Advanced Deep Learning Models Module
Provides state-of-the-art models for well log imputation with graceful fallbacks.

This module implements the Phase 1 foundation for advanced deep learning models
including SAITS, BRITS, Enhanced U-Net, Transformer, and mRNN models.
"""

import warnings
from typing import Dict, Any, Optional

# Safe imports with fallback handling
print("Loading advanced deep learning models...")

# Initialize availability status
ADVANCED_MODELS_STATUS = {
    'saits': False,
    'brits': False,
    'enhanced_unet': False
}

# Model classes (will be set if available)
SAITSModel = None
BRITSModel = None
EnhancedUNet = None
# TransformerModel and MRNNModel have been removed

# Try to import SAITS model
try:
    from .saits_model import SAITSModel
    ADVANCED_MODELS_STATUS['saits'] = True
    print("SAITS model loaded successfully")
except ImportError as e:
    print(f"SAITS model not available: {e}")
    SAITSModel = None

# Try to import BRITS model
try:
    from .brits_model import BRITSModel
    ADVANCED_MODELS_STATUS['brits'] = True
    print("BRITS model loaded successfully")
except ImportError as e:
    print(f"BRITS model not available: {e}")
    BRITSModel = None

# Try to import Enhanced UNet model
try:
    from .enhanced_unet import EnhancedUNet
    ADVANCED_MODELS_STATUS['enhanced_unet'] = True
    print("Enhanced UNet model loaded successfully")
except ImportError as e:
    print(f"Enhanced UNet model not available: {e}")
    EnhancedUNet = None

# Transformer and mRNN models have been removed
TransformerModel = None
MRNNModel = None

# Calculate overall availability
ADVANCED_MODELS_AVAILABLE = any(ADVANCED_MODELS_STATUS.values())

# Report status
if ADVANCED_MODELS_AVAILABLE:
    available_models = [k for k, v in ADVANCED_MODELS_STATUS.items() if v]
    print(f"Advanced models loaded: {available_models}")
    print(f"Total available: {len(available_models)}/5 models")
else:
    print("No advanced models available")
    print("This is expected during Phase 1 - models will be implemented in subsequent phases")

def get_available_models() -> Dict[str, bool]:
    """
    Get the availability status of all advanced models.
    
    Returns:
        Dict[str, bool]: Dictionary mapping model names to availability status
    """
    return ADVANCED_MODELS_STATUS.copy()

def get_model_class(model_name: str) -> Optional[Any]:
    """
    Get the model class for a given model name.

    Args:
        model_name: Name of the model ('saits', 'brits', 'enhanced_unet')

    Returns:
        Model class if available, None otherwise
    """
    model_map = {
        'saits': SAITSModel,
        'brits': BRITSModel,
        'enhanced_unet': EnhancedUNet
    }

    # Handle deprecated models
    if model_name in ['transformer', 'mrnn']:
        warnings.warn(f"Model {model_name} has been removed. Please use SAITS, BRITS, autoencoder, or UNet models instead.")
        return None

    if model_name not in model_map:
        warnings.warn(f"Unknown model name: {model_name}")
        return None

    model_class = model_map[model_name]
    if model_class is None:
        warnings.warn(f"Model {model_name} is not available. Check dependencies and implementation.")

    return model_class

def check_dependencies() -> Dict[str, Any]:
    """
    Check the status of dependencies required for advanced models.
    
    Returns:
        Dict with dependency status information
    """
    dependency_status = {
        'pypots_available': False,
        'monai_available': False,
        'transformers_available': False,
        'einops_available': False,
        'missing_dependencies': [],
        'recommendations': []
    }
    
    # Check PyPOTS
    try:
        import pypots
        dependency_status['pypots_available'] = True
    except ImportError:
        dependency_status['missing_dependencies'].append('pypots')
        dependency_status['recommendations'].append('pip install pypots>=0.2.0')
    
    # Check MONAI
    try:
        import monai
        dependency_status['monai_available'] = True
    except ImportError:
        dependency_status['missing_dependencies'].append('monai')
        dependency_status['recommendations'].append('pip install monai>=0.9.0')
    
    # Check Transformers
    try:
        import transformers
        dependency_status['transformers_available'] = True
    except ImportError:
        dependency_status['missing_dependencies'].append('transformers')
        dependency_status['recommendations'].append('pip install transformers>=4.30.0')
    
    # Check Einops
    try:
        import einops
        dependency_status['einops_available'] = True
    except ImportError:
        dependency_status['missing_dependencies'].append('einops')
        dependency_status['recommendations'].append('pip install einops>=0.7.0')
    
    return dependency_status

# Export public interface
__all__ = [
    'SAITSModel',
    'BRITSModel',
    'EnhancedUNet',
    'ADVANCED_MODELS_AVAILABLE',
    'ADVANCED_MODELS_STATUS',
    'get_available_models',
    'get_model_class',
    'check_dependencies'
]

# Phase 1 completion marker
print("Advanced models module initialized (Phase 1 foundation)")
print("Ready for Phase 2: Core model implementations")
