# ML Log Prediction - Installation Guide

## ⚠️ Addressing Common Installation Issues

This guide addresses the common warnings encountered when setting up the ML Log Prediction environment:

- **WARNING: Failed to find MSVC**
- **WARNING: Failed to find Windows SDK**  
- **WARNING: Failed to find CUDA**

## 🚀 Quick Start

### 1. Run the Automated Setup
```bash
python setup_environment.py
```

This script will check your system and guide you through the installation process.

## 📋 System Requirements

### Minimum Requirements
- **OS**: Windows 10/11 (64-bit)
- **Python**: 3.8 or higher
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space
- **GPU**: Optional but recommended for faster training

### For GPU Support (Optional)
- **NVIDIA GPU** with Compute Capability 3.5+
- **CUDA**: 11.8 or 12.x
- **cuDNN**: Compatible with your CUDA version

## 🔧 Fixing MSVC Issues

### Option 1: Microsoft C++ Build Tools (Recommended)
1. Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
2. Run the installer
3. Select **"C++ build tools"** workload
4. Include these components:
   - MSVC v143 - VS 2022 C++ x64/x86 build tools
   - Windows 10/11 SDK (latest)
   - C++ CMake tools for Windows (optional)

### Option 2: Visual Studio Community
1. Download from: https://visualstudio.microsoft.com/downloads/
2. Select **"Desktop development with C++"** workload
3. Install with default settings

### Verification
```bash
# Check if MSVC is available
cl.exe
```

## 🪟 Fixing Windows SDK Issues

### Method 1: Through Visual Studio Installer
1. Open Visual Studio Installer
2. Click **"Modify"** on your VS installation
3. Go to **"Individual Components"**
4. Search for **"Windows 10/11 SDK"**
5. Select the latest version and install

### Method 2: Direct Download
1. Go to: https://developer.microsoft.com/en-us/windows/downloads/windows-sdk/
2. Download Windows SDK installer
3. Run installer with default settings

### Verification
```bash
# Check Windows SDK installation
dir "C:\Program Files (x86)\Windows Kits\10\Include"
```

## 🎮 Fixing CUDA Issues

### Important Notes
- CUDA is **optional** for basic ML operations
- Only required for GPU-accelerated training
- Ensure your GPU supports CUDA

### Installation Steps

#### 1. Check GPU Compatibility
```bash
# Run this in PowerShell
nvidia-smi
```

#### 2. Download CUDA
- **CUDA 11.8**: https://developer.nvidia.com/cuda-11-8-0-download-archive
- **CUDA 12.x**: https://developer.nvidia.com/cuda-downloads

#### 3. Installation Process
1. Download CUDA Toolkit installer
2. Run installer as Administrator
3. Select **"Express"** installation
4. Restart your computer

#### 4. Install cuDNN
1. Download from: https://developer.nvidia.com/cudnn
2. Extract files to CUDA installation directory
3. Add CUDA to PATH:
   ```
   C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin
   C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\libnvvp
   ```

### Verification
```bash
# Check CUDA installation
nvcc --version
nvidia-smi
```

## 🐍 Python Environment Setup

### 1. Create Virtual Environment
```bash
# Using venv
python -m venv ml_log_env
ml_log_env\Scripts\activate

# Or using conda
conda create -n ml_log_env python=3.9
conda activate ml_log_env
```

### 2. Install Dependencies
```bash
# Upgrade pip
python -m pip install --upgrade pip

# Install requirements
pip install -r requirements.txt

# For GPU support (if CUDA is installed)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 3. Test Installation
```bash
# Run basic test
python -c "import torch; print(torch.cuda.is_available())"
# Should print True if CUDA is working
```

## 🛠️ Environment Activation

### Using the Activation Script
```bash
# Run this script to set up environment variables
activate_ml_env.bat
```

### Manual Environment Setup
```bash
# Set environment variables
set CUDA_PATH=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8
set PATH=%CUDA_PATH%\bin;%PATH%
set PYTHONPATH=%CD%;%PYTHONPATH%
```

## 📊 Testing Your Setup

### 1. Basic Import Test
```python
# test_setup.py
import torch
import numpy as np
import pandas as pd
import sklearn
import transformers

print("✅ All packages imported successfully")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device: {torch.cuda.get_device_name(0)}")
```

### 2. Run the Application
```bash
python main.py --test
```

## 🔍 Troubleshooting

### Common Issues and Solutions

#### 1. "No module named 'torch'"
```bash
pip install torch torchvision torchaudio
```

#### 2. "Microsoft Visual C++ 14.0 is required"
Install Microsoft C++ Build Tools (see MSVC section above)

#### 3. "CUDA out of memory"
- Reduce batch size in configuration
- Use CPU instead: `python main.py --device cpu`

#### 4. "DLL load failed"
- Ensure CUDA and cuDNN versions match
- Check PATH environment variable
- Restart your computer after CUDA installation

#### 5. ImportError: numpy.core.multiarray failed to import
```bash
pip uninstall numpy
pip install numpy
```

## 📞 Getting Help

### Debug Information Collection
```bash
# Create debug report
python setup_environment.py > debug_report.txt 2>&1
```

### Support Resources
- **GitHub Issues**: [Create an issue](https://github.com/your-repo/issues)
- **Documentation**: Check the `/docs` folder
- **Community**: Join our Discord/Slack channel

## 🎯 Next Steps

1. **Complete Installation**: Follow all steps above
2. **Run Setup Script**: `python setup_environment.py`
3. **Test Environment**: Run the test scripts
4. **Start Development**: `python main.py`
5. **Explore Features**: Check the documentation

## 🔄 Updating Dependencies

```bash
# Update all packages
pip install --upgrade -r requirements.txt

# Update specific packages
pip install --upgrade torch torchvision
pip install --upgrade transformers
```

## 📋 System Checklist

- [ ] Python 3.8+ installed
- [ ] Microsoft C++ Build Tools installed
- [ ] Windows SDK installed
- [ ] CUDA installed (optional)
- [ ] Virtual environment created
- [ ] Dependencies installed
- [ ] Environment variables set
- [ ] Basic tests passed

## 🎉 Success Indicators

When your setup is complete, you should see:
- ✅ No warnings about MSVC, Windows SDK, or CUDA
- ✅ All Python packages import without errors
- ✅ GPU detected (if CUDA installed)
- ✅ Application runs successfully

---

**Need more help?** Check the `debug_report.txt` file created by the setup script for detailed error information.