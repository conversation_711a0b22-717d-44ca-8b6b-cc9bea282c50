#!/usr/bin/env python3
"""
Environment Setup Script for ML Log Prediction
==============================================

This script helps set up the development environment for the ML Log Prediction project,
addressing common issues with MSVC, Windows SDK, and CUDA on Windows systems.
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil
from pathlib import Path

class EnvironmentSetup:
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        self.python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
        
    def check_system_requirements(self):
        """Check system requirements and report issues"""
        print("🔍 Checking system requirements...")
        
        # Check Python version
        print(f"Python version: {sys.version}")
        if sys.version_info < (3, 8):
            print("❌ Python 3.8+ required")
            return False
        
        # Check if running on Windows
        if not self.is_windows:
            print("❌ This setup script is designed for Windows")
            return False
            
        print("✅ Basic requirements met")
        return True
    
    def check_msvc_installation(self):
        """Check for Microsoft Visual C++ Build Tools"""
        print("\n🔍 Checking for MSVC...")
        
        # Common MSVC locations
        msvc_paths = [
            r"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools",
            r"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools",
            r"C:\Program Files\Microsoft Visual Studio\2019\BuildTools",
            r"C:\Program Files\Microsoft Visual Studio\2022\BuildTools",
            r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community",
            r"C:\Program Files (x86)\Microsoft Visual Studio\2022\Community"
        ]
        
        msvc_found = any(os.path.exists(path) for path in msvc_paths)
        
        if msvc_found:
            print("✅ MSVC found")
            return True
        else:
            print("❌ MSVC not found")
            print("💡 Download Microsoft C++ Build Tools from:")
            print("   https://visualstudio.microsoft.com/visual-cpp-build-tools/")
            return False
    
    def check_windows_sdk(self):
        """Check for Windows SDK"""
        print("\n🔍 Checking for Windows SDK...")
        
        # Common Windows SDK locations
        sdk_paths = [
            r"C:\Program Files (x86)\Windows Kits\10\Include",
            r"C:\Program Files\Windows Kits\10\Include"
        ]
        
        sdk_found = any(os.path.exists(path) for path in sdk_paths)
        
        if sdk_found:
            print("✅ Windows SDK found")
            return True
        else:
            print("❌ Windows SDK not found")
            print("💡 Install Windows SDK through Visual Studio Installer")
            print("   or download from: https://developer.microsoft.com/en-us/windows/downloads/windows-sdk/")
            return False
    
    def check_cuda_installation(self):
        """Check for CUDA installation"""
        print("\n🔍 Checking for CUDA...")
        
        # Check CUDA path
        cuda_path = os.environ.get('CUDA_PATH', r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA")
        
        if os.path.exists(cuda_path):
            print("✅ CUDA found at:", cuda_path)
            
            # Check CUDA version
            try:
                nvcc_path = os.path.join(cuda_path, 'v*', 'bin', 'nvcc.exe')
                import glob
                nvcc_files = glob.glob(nvcc_path)
                if nvcc_files:
                    result = subprocess.run([nvcc_files[0], '--version'], 
                                          capture_output=True, text=True)
                    print("CUDA version info:")
                    print(result.stdout.split('\n')[3] if len(result.stdout.split('\n')) > 3 else "Version check failed")
            except:
                pass
                
            return True
        else:
            print("❌ CUDA not found")
            print("💡 Download CUDA from: https://developer.nvidia.com/cuda-downloads")
            print("   Note: CUDA is optional for basic ML operations")
            return False
    
    def install_python_dependencies(self):
        """Install Python dependencies"""
        print("\n📦 Installing Python dependencies...")
        
        # Upgrade pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install wheel for better package support
        subprocess.run([sys.executable, "-m", "pip", "install", "wheel"])
        
        # Install requirements
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Python dependencies installed successfully")
            return True
        else:
            print("❌ Error installing dependencies:")
            print(result.stderr)
            return False
    
    def create_activation_script(self):
        """Create activation script for environment variables"""
        print("\n📝 Creating activation script...")
        
        activate_script = """
@echo off
REM ML Log Prediction Environment Activation Script
REM Run this script to set up environment variables

echo Setting up ML Log Prediction environment...

REM Add CUDA to PATH if installed
if exist "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v*" (
    for /f "tokens=*" %%i in ('dir "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v*" /b /ad-h /o-n') do (
        set "CUDA_PATH=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\%%i"
        set "PATH=%CUDA_PATH%\bin;%PATH%"
        echo CUDA %%i added to PATH
    )
)

REM Set Python path
set "PYTHONPATH=%CD%;%PYTHONPATH%"

echo Environment setup complete!
echo.
echo Available commands:
echo   python main.py          - Run the main application
echo   python -m pytest tests  - Run tests
echo   jupyter notebook        - Start Jupyter notebook
pause
"""
        
        with open("activate_ml_env.bat", "w") as f:
            f.write(activate_script)
        
        print("✅ Activation script created: activate_ml_env.bat")
    
    def run_setup(self):
        """Run complete setup process"""
        print("🚀 ML Log Prediction Environment Setup")
        print("=" * 50)
        
        if not self.check_system_requirements():
            return False
        
        # Check components
        msvc_ok = self.check_msvc_installation()
        sdk_ok = self.check_windows_sdk()
        cuda_ok = self.check_cuda_installation()
        
        # Install Python dependencies
        python_ok = self.install_python_dependencies()
        
        # Create activation script
        self.create_activation_script()
        
        # Summary
        print("\n" + "=" * 50)
        print("📋 Setup Summary")
        print("=" * 50)
        print(f"MSVC: {'✅' if msvc_ok else '❌'}")
        print(f"Windows SDK: {'✅' if sdk_ok else '❌'}")
        print(f"CUDA: {'✅' if cuda_ok else '❌ (optional)'}")
        print(f"Python Dependencies: {'✅' if python_ok else '❌'}")
        
        if not (msvc_ok and sdk_ok):
            print("\n⚠️  Manual installation required for:")
            if not msvc_ok:
                print("   - Microsoft C++ Build Tools")
            if not sdk_ok:
                print("   - Windows SDK")
        
        print("\n🎯 Next steps:")
        print("1. Install missing components if any")
        print("2. Run 'activate_ml_env.bat' to set up environment")
        print("3. Run 'python main.py' to test the application")
        
        return python_ok

if __name__ == "__main__":
    setup = EnvironmentSetup()
    setup.run_setup()