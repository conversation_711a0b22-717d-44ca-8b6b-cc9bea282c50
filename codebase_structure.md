# ML Log Prediction - Main Codebase Structure

## Overview
This document outlines the core structure of the ML Log Prediction pipeline accessed from `main.py`. The system provides a comprehensive workflow for loading, processing, and predicting well log data using various machine learning models with GPU acceleration and memory optimization.

## Core File Dependencies Accessed from `main.py`

### **Primary Module Imports**
```python
# Data handling and preprocessing
from data_handler import load_las_files_from_directory, clean_log_data, write_results_to_las

# User interface and configuration
from config_handler import get_input_files, select_output_directory, configure_log_selection, 
                          configure_well_separation, get_prediction_mode, configure_hyperparameters, console_select

# Machine learning core functionality
from ml_core import impute_logs, impute_logs_deep, MODEL_REGISTRY

# Enhanced Phase 1 preprocessing (optional)
from ml_core_phase1_integration import impute_logs_deep_phase1

# Visualization and reporting
from reporting import generate_qc_report, create_summary_plots, generate_final_report,
                     create_multi_model_comparison_plots, create_separate_comparison_plots,
                     create_crossplot_analysis, create_model_ranking_visualization

# Memory optimization utilities
from utils.memory_optimization import get_memory_optimizer
```

### **Core Module Structure**

#### `data_handler.py` - Data Operations
- **Functions accessed**:
  - `load_las_files_from_directory()`: Processes LAS files into pandas DataFrames
  - `clean_log_data()`: Applies domain-specific cleaning rules
  - `write_results_to_las()`: Exports results back to LAS format

#### `config_handler.py` - User Interface & Configuration
- **Functions accessed**:
  - `get_input_files()`: Interactive GUI for LAS file selection
  - `select_output_directory()`: Output directory selection
  - `configure_log_selection()`: Feature and target log configuration
  - `configure_well_separation()`: Training/prediction strategy setup
  - `get_prediction_mode()`: Prediction mode configuration
  - `configure_hyperparameters()`: Model hyperparameter settings
  - `console_select()`: Multi-option console selection interface

#### `ml_core.py` - Machine Learning Engine
- **Functions accessed**:
  - `impute_logs()`: Shallow model execution (XGBoost, LightGBM, CatBoost)
  - `impute_logs_deep()`: Deep learning model execution
  - `MODEL_REGISTRY`: Central registry of all available models

#### `ml_core_phase1_integration.py` - Enhanced Preprocessing (Optional)
- **Functions accessed**:
  - `impute_logs_deep_phase1()`: Enhanced deep learning with improved preprocessing

#### `reporting.py` - Visualization & Analysis
- **Functions accessed**:
  - `generate_qc_report()`: Initial data quality reports
  - `create_summary_plots()`: Standard model performance visualizations
  - `create_separate_comparison_plots()`: Original vs Imputed/Predicted plots
  - `create_crossplot_analysis()`: Quality control cross-plots
  - `create_multi_model_comparison_plots()`: Comparative visualizations
  - `create_model_ranking_visualization()`: Performance ranking charts
  - `generate_final_report()`: Detailed performance metrics

#### `utils/memory_optimization.py` - Memory Management
- **Functions accessed**:
  - `get_memory_optimizer()`: Memory optimization context manager

### **Directory Structure Accessed**

#### `models/` - Model Implementations
- **Shallow Models**: XGBoost, LightGBM, CatBoost configurations
- **Deep Learning Models**: Autoencoder, U-Net implementations
- **Advanced Models**: SAITS, BRITS, Transformer, MRNN (in `advanced_models/`)

#### `utils/` - Utility Modules
- **GPU acceleration**: CUDA optimization and fallback strategies
- **Memory optimization**: Large dataset handling and OOM recovery
- **Performance monitoring**: Training and inference benchmarking

#### `Las/` - Input Data
- **LAS Files**: Well log data in Log ASCII Standard format

#### `config/` - Configuration
- **Display settings**: Visualization and output formatting

#### `plots/` - Generated Outputs
- **Visualization results**: Model performance and comparison plots

## Main Pipeline Structure (`main.py`)

The main entry point orchestrates the complete ML log prediction workflow through these key stages:

### 1. Environment Configuration (Phase 1)
- Sets `PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True` for memory optimization
- Initializes memory optimizer with mixed precision and monitoring
- Loads Phase 1 enhanced deep learning integration

### 2. Data Loading & Configuration
- **File Selection**: Interactive GUI for selecting LAS files
- **Data Loading**: `load_las_files_from_directory()` processes LAS files into pandas DataFrames
- **Log Configuration**: 
  - Feature log selection (`configure_log_selection()`)
  - Target log specification
  - Well separation strategy (`configure_well_separation()`)
  - Prediction mode selection (`get_prediction_mode()`)

### 3. Data Preprocessing
- **Quality Control**: `clean_log_data()` applies domain-specific cleaning rules
- **QC Reporting**: `generate_qc_report()` creates initial data quality reports

### 4. Model Selection & Execution Loop
- **Interactive Model Selection**: Console-based interface for choosing models
- **Multi-Model Support**: Run single or multiple models in batch
- **Model Categories**:
  - Gradient Boosting (XGBoost, LightGBM, CatBoost) - GPU accelerated
  - Deep Learning (SAITS, BRITS, Transformer, U-Net, Autoencoder)
  - Statistical Models (Linear Regression, Ridge)

### 5. Model Execution Pipeline
For each selected model:
- **Shallow Models** (XGBoost, LightGBM, CatBoost):
  - Direct execution through `impute_logs()`
  - GPU acceleration with automatic fallback
- **Deep Models**:
  - Phase 1 enhanced training via `impute_logs_deep_phase1()` (if available)
  - Memory-optimized context through `memory_optimizer.memory_efficient_context()`
  - Fallback to standard `impute_logs_deep()` if Phase 1 not available

### 6. Results Processing & Evaluation
- **Performance Metrics**: MAE, R², RMSE, Composite Score
- **Automatic Ranking**: Models sorted by composite performance scores
- **Result Storage**: All successful model results stored for comparison

### 7. Output & Visualization Options
Multiple output paths depending on single/multi model scenarios:
- **Single Model**:
  1. Save results to files (LAS + plots)
  2. Enhanced visualization analysis
  3. Detailed single model analysis
  4. Quality control analysis
- **Multiple Models**:
  1. Save results (select specific model)
  2. Enhanced visualization (select specific model)
  3. Comprehensive multi-model comparison
  4. Quality control analysis (select specific model)
  5. Side-by-side model comparison plots
  6. Generate comparison report for all models

### 8. Reporting Functions
- `create_summary_plots()`: Standard model performance visualizations
- `create_separate_comparison_plots()`: Original vs Imputed/Predicted plots
- `create_crossplot_analysis()`: Quality control cross-plots
- `create_multi_model_comparison_plots()`: Comparative visualizations
- `create_model_ranking_visualization()`: Performance ranking charts
- `generate_final_report()`: Detailed performance metrics reports

### 9. Iterative Workflow
- Continue/Exit options after each run
- Ability to re-run plotting for previous results
- Persistent model results during session

### 10. Memory Management (Phase 1)
- Final memory status reporting
- Memory optimization throughout execution
- GPU memory monitoring and management

## Workflow Execution Dependencies

### **Direct Function Calls from `main.py`**

#### Data Loading & Configuration Phase
```python
# Step 1-2: File selection and data loading
inp = get_input_files()  # config_handler.py
df, las_objs, wells, logs = load_las_files_from_directory(inp)  # data_handler.py

# Step 3-5: Configuration setup
feats, tgt = configure_log_selection(logs)  # config_handler.py
cfg = configure_well_separation(wells)  # config_handler.py
mode = get_prediction_mode()  # config_handler.py
hparams = configure_hyperparameters()  # config_handler.py
```

#### Data Preprocessing Phase
```python
# Step 6-7: Data cleaning and quality control
clean_df = clean_log_data(df)  # data_handler.py
generate_qc_report(clean_df, feats+[tgt], cfg)  # reporting.py
```

#### Model Execution Phase
```python
# Step 8: Model selection and execution
selected_model_keys = console_select(available_models, ...)  # config_handler.py

# For shallow models (XGBoost, LightGBM, CatBoost)
res_df, mres = impute_logs(clean_df, feats, tgt, models_to_run, cfg, mode)  # ml_core.py

# For deep learning models with Phase 1 enhancement
res_df, mres = impute_logs_deep_phase1(clean_df, feats, tgt, selected_model_config, hparams_selected)  # ml_core_phase1_integration.py

# For standard deep learning models (fallback)
res_df, mres = impute_logs_deep(clean_df, feats, tgt, selected_model_config, hparams_selected)  # ml_core.py
```

#### Visualization & Output Phase
```python
# Step 9: Output and visualization options
create_summary_plots(res_df, mres, cfg, model_name=model_key, show_error_bands=True)  # reporting.py
create_separate_comparison_plots(res_df, mres, cfg, model_name=model_key)  # reporting.py
create_crossplot_analysis(res_df, mres, cfg, model_name=model_key, color_by='well')  # reporting.py
create_multi_model_comparison_plots(all_results, cfg, tgt)  # reporting.py
create_model_ranking_visualization(all_results, tgt, combined_evaluations)  # reporting.py
generate_final_report(mres)  # reporting.py
write_results_to_las(res_df, tgt, las_objs, out)  # data_handler.py
```

#### Memory Management Integration
```python
# Phase 1 memory optimization throughout execution
memory_optimizer = get_memory_optimizer(enable_mixed_precision=True, enable_monitoring=True)  # utils.memory_optimization
with memory_optimizer.memory_efficient_context():  # Context manager for GPU memory
    # Model execution with memory optimization
```

### **Model Registry Access Pattern**
```python
# Central model registry accessed for all model configurations
MODEL_REGISTRY = {
    'xgboost': {'type': 'shallow', 'model_class': XGBRegressor, ...},
    'saits': {'type': 'deep_advanced', 'model_class': SAITSModel, ...},
    'transformer': {'type': 'deep_advanced', 'model_class': TransformerModel, ...},
    ...
}
available_models = list(MODEL_REGISTRY.keys())  # ml_core.py
selected_model_config = MODEL_REGISTRY[model_key]  # ml_core.py
```

## Complete Execution Flow

```
main() 
├── [Phase 1] Environment Setup
│   ├── os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
│   ├── memory_optimizer = get_memory_optimizer() → utils.memory_optimization
│   └── import ml_core_phase1_integration (optional)
│
├── [Steps 1-2] File Selection & Data Loading
│   ├── inp = get_input_files() → config_handler.py
│   └── df, las_objs, wells, logs = load_las_files_from_directory(inp) → data_handler.py
│
├── [Steps 3-5] Configuration Setup
│   ├── feats, tgt = configure_log_selection(logs) → config_handler.py
│   ├── cfg = configure_well_separation(wells) → config_handler.py
│   ├── mode = get_prediction_mode() → config_handler.py
│   └── hparams = configure_hyperparameters() → config_handler.py
│
├── [Steps 6-7] Data Preprocessing
│   ├── clean_df = clean_log_data(df) → data_handler.py
│   └── generate_qc_report(clean_df, feats+[tgt], cfg) → reporting.py
│
├── [Step 8] Model Selection & Execution Loop
│   ├── available_models = list(MODEL_REGISTRY.keys()) → ml_core.py
│   ├── selected_model_keys = console_select(...) → config_handler.py
│   ├── For each model:
│   │   ├── Shallow Models → impute_logs() → ml_core.py
│   │   ├── Deep Models (Phase 1) → impute_logs_deep_phase1() → ml_core_phase1_integration.py
│   │   └── Deep Models (Standard) → impute_logs_deep() → ml_core.py
│   ├── Results Collection & Performance Evaluation
│   └── Model Performance Summary Display
│
├── [Step 9] Output & Visualization Options
│   ├── Single Model Path:
│   │   ├── create_summary_plots() → reporting.py
│   │   ├── create_separate_comparison_plots() → reporting.py
│   │   ├── create_crossplot_analysis() → reporting.py
│   │   └── write_results_to_las() → data_handler.py
│   └── Multi-Model Path:
│       ├── create_multi_model_comparison_plots() → reporting.py
│       ├── create_model_ranking_visualization() → reporting.py
│       └── generate_final_report() → reporting.py
│
└── [Step 10] Continue/Exit Decision Loop
    ├── Continue → Return to Step 8
    ├── Go back to Step 9 → Repeat visualization options
    └── Exit → Final memory status report
```