Flexible Split Report:
  - Wells for Training/Validation: 1495
  - Wells for Final Testing: 374
  - Train Samples (Shallow part of train wells): 669536
  - Validation Samples (Deeper part of train wells): 286944
  - Test Samples (Entirely separate wells): 239360

Running data leakage detection on splits...
🔍 Performing comprehensive data leakage check...
============================================================
🔍 Detecting perfect correlation leakage...
✅ No perfect correlation leakage detected
🕐 Validating temporal split integrity...
✅ No temporal leakage detected in splits
🎯 Detecting target leakage in features...
✅ No target leakage detected in features
============================================================
📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY
============================================================
✅ NO DATA LEAKAGE DETECTED!
   All checks passed successfully
   Data quality score: 1.00
============================================================

Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=669536/669536 (100.0%)
Normalized 'NPHI': method=standard, valid_data=669536/669536 (100.0%)
Normalized 'RHOB': method=standard, valid_data=669536/669536 (100.0%)
Normalized 'RT': method=standard, valid_data=669536/669536 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=669536/669536 (100.0%)
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_1523: 100%|██████████| 1495/1495 wells [05:13<00:00,  4.77well/s]

📊 Well Processing Summary:
   • Total wells processed: 1495
   • Successful: 1495
   • Failed: 0
   • Total valid intervals: 1,495
   • Total sequences created: 575,351
Enhanced sequences shape: (575351, 64, 5), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced normalization with winsorization...
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_1523: 100%|██████████| 1495/1495 wells [01:56<00:00, 12.83well/s]

📊 Well Processing Summary:
   • Total wells processed: 1495
   • Successful: 1495
   • Failed: 0
   • Total valid intervals: 1,495
   • Total sequences created: 192,759
Enhanced sequences shape: (192759, 64, 5), type: <class 'numpy.ndarray'>, dtype: float64
📚 Introducing missingness for imputation training.
Using enhanced missing value introduction with realistic patterns...
Introduced 26.1% missing values (47984029 elements)
Pattern: 16570108 random + 31413921 chunked
Enhanced missing sequences shape: (575351, 64, 5), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced missing value introduction with realistic patterns...
Introduced 26.1% missing values (16078679 elements)
Pattern: 5551459 random + 10527220 chunked
Enhanced missing sequences shape: (192759, 64, 5), type: <class 'numpy.ndarray'>, dtype: float64
Converting sequences to tensors...
   Processing train_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing train_sequences_missing dtype from float64 to float32.
   train_sequences_missing tensor created with shape: torch.Size([575351, 64, 5]), dtype: torch.float32
   Processing train_sequences_true: current type is <class 'numpy.ndarray'>
   Changing train_sequences_true dtype from float64 to float32.
   train_sequences_true tensor created with shape: torch.Size([575351, 64, 5]), dtype: torch.float32
   Processing val_sequences_missing: current type is <class 'numpy.ndarray'>
   Changing val_sequences_missing dtype from float64 to float32.
   val_sequences_missing tensor created with shape: torch.Size([192759, 64, 5]), dtype: torch.float32
   Processing val_sequences_true: current type is <class 'numpy.ndarray'>
   Changing val_sequences_true dtype from float64 to float32.
   val_sequences_true tensor created with shape: torch.Size([192759, 64, 5]), dtype: torch.float32
   Tensor shapes - Train: torch.Size([575351, 64, 5]), Truth: torch.Size([575351, 64, 5])
🔧 Applied fixed parameters for Autoencoder: {}
🚀 Selected GPU 0: NVIDIA GeForce RTX 2070 Super
   Memory: 8.6 GB
   Compute Capability: 7.5
⚡ Mixed precision training available
✅ GPU initialized successfully
🚀 Using GPU with fallback protection: cuda
⚡ Mixed precision training enabled
🔥 Warming up GPU...
✅ GPU warm-up completed
✅ Autoencoder model created successfully
Training phase...
   About to train model with:
   - train_tensor type: <class 'torch.Tensor'>, shape: torch.Size([575351, 64, 5]), dtype: torch.float32    
   - truth_tensor type: <class 'torch.Tensor'>, shape: torch.Size([575351, 64, 5]), dtype: torch.float32    
🔧 Initializing training stability utilities...
INFO:utils.stability_core:🔧 Initialized gradient clipper for autoencoder with max_norm=2.0
INFO:utils.stability_core:🚀 Initialized autoencoder LR scheduler: warmup=1000, base_lr=0.0005
   ✅ AdaptiveLRScheduler initialized for autoencoder
   ✅ Gradient clipper (autoencoder) and LR scheduler initialized
Training with DataLoader interface: Autoencoder
   DataLoader configured with batch_size=128, num_workers=0, pin_memory=True
🔧 Training with direct tensor interface: Autoencoder
Filtered data: using 575212/575351 samples (removed 139 high-missing samples)
Training simple autoencoder for 30 epochs...
Data shape: torch.Size([575212, 320])
Missing data percentage: 26.06%
Device: cuda
📊 Performance monitoring started
Replaced NaN values with 0 for training
Epoch 0/30, Loss: 0.245905
   ⏱️ Epoch time: 50.08s, Memory Δ: +10.5MB
   ⏱️ Epoch time: 50.25s, Memory Δ: +0.0MB
Epoch 2/30, Loss: 0.229524
   ⏱️ Epoch time: 61.46s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 61.63s, Memory Δ: +0.0MB
Epoch 4/30, Loss: 0.227696
   ⏱️ Epoch time: 62.51s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 60.35s, Memory Δ: +0.0MB
Epoch 6/30, Loss: 0.225842
   ⏱️ Epoch time: 50.51s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 48.03s, Memory Δ: +0.0MB
Epoch 8/30, Loss: 0.224820
   ⏱️ Epoch time: 48.23s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 49.30s, Memory Δ: +0.0MB
Epoch 10/30, Loss: 0.223692
   ⏱️ Epoch time: 43.79s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 46.85s, Memory Δ: +0.0MB
Epoch 12/30, Loss: 0.222402
   ⏱️ Epoch time: 46.28s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 46.30s, Memory Δ: +0.0MB
Epoch 14/30, Loss: 0.221789
   ⏱️ Epoch time: 46.09s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 42.33s, Memory Δ: +0.0MB
Epoch 16/30, Loss: 0.221232
   ⏱️ Epoch time: 41.37s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 44.76s, Memory Δ: +0.0MB
Epoch 18/30, Loss: 0.220745
   ⏱️ Epoch time: 46.42s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 42.20s, Memory Δ: +0.0MB
Epoch 20/30, Loss: 0.220135
   ⏱️ Epoch time: 42.70s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 45.63s, Memory Δ: +0.0MB
Epoch 22/30, Loss: 0.219590
   ⏱️ Epoch time: 45.93s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 45.00s, Memory Δ: +0.0MB
Epoch 24/30, Loss: 0.219238
   ⏱️ Epoch time: 47.47s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 47.24s, Memory Δ: +0.0MB
Epoch 26/30, Loss: 0.218959
   ⏱️ Epoch time: 44.15s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 46.48s, Memory Δ: +0.0MB
Epoch 28/30, Loss: 0.218852
   ⏱️ Epoch time: 42.88s, Memory Δ: +0.0MB
   ⏱️ Epoch time: 53.06s, Memory Δ: +0.0MB
Training completed!
📊 Performance monitoring stopped

📊 Training Performance Summary:
   • Average Epoch Time: 48.31s
   • Total Training Time: 1449.3s
Enhanced Evaluation Phase...
Imputation Metrics (Artificial Missing Values):
   • MAE: 0.2444
   • R²: 0.8705
   • RMSE: 0.3603
   • Evaluated Points: 3216354
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: PREDICTION
  - Using feature columns only for valid interval detection
  - Target column will be included in sequences as NaN for model prediction
Creating sequences | Current: WELL_1523: 100%|██████████| 1495/1495 wells [02:08<00:00, 11.68well/s]

📊 Well Processing Summary:
   • Total wells processed: 1495
   • Successful: 1495
   • Failed: 0
   • Total valid intervals: 1,495
   • Total sequences created: 192,759
Enhanced sequences shape: (192759, 64, 5), type: <class 'numpy.ndarray'>, dtype: float64

Prediction Metrics (No Target Context):
   • MAE: 0.6041
   • R²: 0.3803
   • RMSE: 0.7880
   • Evaluated Points: 12336576

Evaluating Imputation Performance...
Imputation Metrics (Artificial Missing Values):
   • MAE: 0.2444
   • R²: 0.8705
   • RMSE: 0.3603
   • Samples: 3216354

Evaluating Prediction Performance...
Using enhanced normalization with winsorization...
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: PREDICTION
  - Using feature columns only for valid interval detection
  - Target column will be included in sequences as NaN for model prediction
Creating sequences | Current: WELL_1865: 100%|████████████| 374/374 wells [01:35<00:00,  3.93well/s]

📊 Well Processing Summary:
   • Total wells processed: 374
   • Successful: 374
   • Failed: 0
   • Total valid intervals: 374
   • Total sequences created: 215,798
Enhanced sequences shape: (215798, 64, 5), type: <class 'numpy.ndarray'>, dtype: float64
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: TRAINING
Creating sequences | Current: WELL_1865: 100%|████████████| 374/374 wells [01:41<00:00,  3.70well/s]

📊 Well Processing Summary:
   • Total wells processed: 374
   • Successful: 374
   • Failed: 0
   • Total valid intervals: 374
   • Total sequences created: 215,798
Enhanced sequences shape: (215798, 64, 5), type: <class 'numpy.ndarray'>, dtype: float64
Prediction Metrics (No Target Context):
   • MAE: 0.6325
   • R²: 0.3656
   • RMSE: 0.8431
   • Samples: 13811072

Performance Analysis:
   • R² Drop (Imputation → Prediction): 0.5049
   • Performance Ratio: 42.00%
   • Task Complexity Increase: 2.59x

⚠️ SIGNIFICANT PERFORMANCE DROP DETECTED!
   This indicates the model is optimized for imputation, not prediction.
   Consider:
   1. Using this model only for imputation tasks
   2. Training a separate model for prediction
   3. Using shallow ML models (XGBoost, etc.) for prediction

PERFORMANCE DISCREPANCY DETECTED!
   • Imputation R²: 0.8705
   • Prediction R²: 0.3656
   • Discrepancy: 0.5049

   This model is optimized for imputation, not prediction.
   Consider using shallow ML models for pure prediction tasks.
Prediction phase (full dataset)...
Using enhanced normalization with winsorization...
Normalized 'GR': method=standard, valid_data=1195840/1195840 (100.0%)
Normalized 'NPHI': method=standard, valid_data=1195840/1195840 (100.0%)
Normalized 'RHOB': method=standard, valid_data=1195840/1195840 (100.0%)
Normalized 'RT': method=standard, valid_data=1195840/1195840 (100.0%)
Normalized 'P-WAVE': method=standard, valid_data=1195840/1195840 (100.0%)
Data preparation for prediction:
  - Feature columns filled: ['GR', 'NPHI', 'RHOB', 'RT']
  - Target column 'P-WAVE' NaNs preserved
  - Original NaN count in target: 0
  - Preserved NaN count in target: 1195840
Using enhanced sequence creation with valid interval detection...
Sequence creation mode: PREDICTION
  - Using feature columns only for valid interval detection
  - Target column will be included in sequences as NaN for model prediction
Creating sequences | Current: WELL_1868: 100%|██████████| 1869/1869 wells [09:40<00:00,  3.22well/s]

📊 Well Processing Summary:
   • Total wells processed: 1869
   • Successful: 1869
   • Failed: 0
   • Total valid intervals: 1,869
   • Total sequences created: 1,078,093
Enhanced sequences shape: (1078093, 64, 5), type: <class 'numpy.ndarray'>, dtype: float64
Prediction tensor shape: torch.Size([1078093, 64, 5]), dtype: torch.float32
📦 Using standard prediction for 1,078,093 samples
Model output tensor shape: torch.Size([1078093, 64, 5]), dtype: torch.float16
Converted to numpy - shape: (1078093, 64, 5), dtype: float16
Number of sequences to process: 1078093
Post-processing and re-assembling results...
Debug info for sequence 0:
  predicted_sequence_scaled shape: (64, 5)
  predicted_sequence_scaled dtype: float16
  original_indices length: 64
  all_features length: 5
  pred_sum_df slice shape: (64, 5)
  pred_sum_df dtype: float64