#!/usr/bin/env python3
"""
Windows Setup Script for Deep Learning Environment

This script helps install missing Windows components needed for deep learning:
- Microsoft Visual C++ (MSVC) Build Tools
- Windows SDK
- CUDA Toolkit (if not installed)

Usage:
    python scripts/windows_setup.py
"""

import os
import sys
import subprocess
import urllib.request
import json
from pathlib import Path
from typing import List, Dict, Optional

class WindowsSetupManager:
    """Manages Windows development environment setup for ML projects."""
    
    def __init__(self):
        self.is_windows = os.name == 'nt'
        self.missing_components = []
        
    def check_system_requirements(self) -> Dict[str, bool]:
        """Check which Windows components are missing."""
        requirements = {
            'msvc': self._check_msvc(),
            'windows_sdk': self._check_windows_sdk(),
            'cuda': self._check_cuda(),
            'cudnn': self._check_cudnn()
        }
        
        self.missing_components = [k for k, v in requirements.items() if not v]
        return requirements
    
    def _check_msvc(self) -> bool:
        """Check if Microsoft Visual C++ Build Tools are installed."""
        try:
            # Check for cl.exe in common locations
            msvc_paths = [
                r"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC",
                r"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC",
                r"C:\Program Files\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC",
                r"C:\Program Files\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC"
            ]
            
            for path in msvc_paths:
                if Path(path).exists():
                    # Look for cl.exe in subdirectories
                    for cl_path in Path(path).rglob("cl.exe"):
                        return True
            
            # Try running cl.exe from PATH
            result = subprocess.run(['where', 'cl'], 
                                  capture_output=True, text=True, shell=True)
            return result.returncode == 0
            
        except Exception:
            return False
    
    def _check_windows_sdk(self) -> bool:
        """Check if Windows SDK is installed."""
        try:
            sdk_paths = [
                r"C:\Program Files (x86)\Windows Kits\10",
                r"C:\Program Files\Windows Kits\10"
            ]
            
            for path in sdk_paths:
                if Path(path).exists():
                    # Check for rc.exe (Resource Compiler)
                    rc_path = Path(path) / "bin" / "x64" / "rc.exe"
                    if rc_path.exists():
                        return True
            
            # Try running rc.exe from PATH
            result = subprocess.run(['where', 'rc'], 
                                  capture_output=True, text=True, shell=True)
            return result.returncode == 0
            
        except Exception:
            return False
    
    def _check_cuda(self) -> bool:
        """Check if CUDA Toolkit is installed."""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            # Check CUDA installation directly
            cuda_paths = [
                r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA",
                r"C:\Program Files\NVIDIA Corporation\CUDA Samples"
            ]
            
            for path in cuda_paths:
                if Path(path).exists():
                    return True
            
            # Check nvcc
            result = subprocess.run(['where', 'nvcc'], 
                                  capture_output=True, text=True, shell=True)
            return result.returncode == 0
    
    def _check_cudnn(self) -> bool:
        """Check if cuDNN is available."""
        try:
            import torch
            return torch.backends.cudnn.is_available()
        except ImportError:
            return False
    
    def generate_installation_guide(self) -> str:
        """Generate installation guide for missing components."""
        if not self.missing_components:
            return "✅ All required components are installed!"
        
        guide = []
        guide.append("🛠️  Windows Development Environment Setup Guide")
        guide.append("=" * 50)
        guide.append("")
        
        if 'msvc' in self.missing_components:
            guide.extend([
                "📦 Microsoft Visual C++ Build Tools:",
                "   1. Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/",
                "   2. Run the installer",
                "   3. Select 'C++ build tools' workload",
                "   4. Include Windows 10/11 SDK",
                "   5. Install and restart your computer",
                ""
            ])
        
        if 'windows_sdk' in self.missing_components:
            guide.extend([
                "📦 Windows SDK:",
                "   1. Download from: https://developer.microsoft.com/windows/downloads/windows-sdk/",
                "   2. Run the Windows SDK installer",
                "   3. Follow installation wizard",
                "   4. Restart your computer after installation",
                ""
            ])
        
        if 'cuda' in self.missing_components:
            guide.extend([
                "📦 NVIDIA CUDA Toolkit:",
                "   1. Check GPU compatibility at: https://developer.nvidia.com/cuda-gpus",
                "   2. Download CUDA Toolkit from: https://developer.nvidia.com/cuda-downloads",
                "   3. Select Windows → x86_64 → Version → exe (local)",
                "   4. Run installer with default settings",
                "   5. Restart your computer",
                ""
            ])
        
        if 'cudnn' in self.missing_components:
            guide.extend([
                "📦 NVIDIA cuDNN:",
                "   1. Register at: https://developer.nvidia.com/cudnn",
                "   2. Download cuDNN for your CUDA version",
                "   3. Extract files to CUDA installation directory",
                "   4. Add CUDA bin directory to PATH",
                ""
            ])
        
        guide.append("💡 After installing missing components:")
        guide.append("   1. Restart your computer")
        guide.append("   2. Run this script again to verify installation")
        guide.append("   3. Re-run your ML training script")
        
        return "\n".join(guide)
    
    def create_batch_installer(self) -> str:
        """Create a batch file for automated installation."""
        batch_content = """@echo off
echo Windows ML Environment Setup
echo ==============================

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Please run as administrator
    pause
    exit /b 1
)

REM Install Chocolatey package manager (if not installed)
where choco >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Chocolatey...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))"
)

REM Install Visual Studio Build Tools
echo Installing Visual Studio Build Tools...
choco install visualstudio2019buildtools --package-parameters "--add Microsoft.VisualStudio.Workload.VCTools" -y

REM Install Windows SDK
echo Installing Windows SDK...
choco install windows-sdk-10.0 -y

REM Install CUDA (optional - user needs to choose version)
echo.
echo To install CUDA Toolkit:
echo 1. Visit: https://developer.nvidia.com/cuda-downloads
echo 2. Download and install for your system
echo.

echo.
echo Installation complete! Please restart your computer.
pause
"""
        
        batch_path = Path("install_windows_tools.bat")
        with open(batch_path, 'w') as f:
            f.write(batch_content)
        
        return str(batch_path)
    
    def run_verification(self) -> bool:
        """Run comprehensive verification of the environment."""
        print("🔍 Running Windows environment verification...")
        
        requirements = self.check_system_requirements()
        
        print("\n📊 System Status:")
        for component, status in requirements.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {component.upper()}: {'Available' if status else 'Missing'}")
        
        if self.missing_components:
            print(f"\n⚠️ Missing components: {', '.join(self.missing_components)}")
            print("\n" + self.generate_installation_guide())
            
            # Create installer batch file
            batch_path = self.create_batch_installer()
            print(f"\n📁 Created installer: {batch_path}")
            print("💡 Run this batch file as administrator to install missing tools")
            
            return False
        else:
            print("\n🎉 All required components are installed!")
            print("✅ Your Windows environment is ready for deep learning!")
            return True

def main():
    """Main setup script."""
    if os.name != 'nt':
        print("❌ This script is designed for Windows systems only.")
        return
    
    setup_manager = WindowsSetupManager()
    
    print("🚀 Windows ML Environment Setup")
    print("=" * 35)
    
    # Run verification
    success = setup_manager.run_verification()
    
    if success:
        print("\n🎯 You can now run your deep learning scripts without warnings!")
    else:
        print("\n🔧 Please install the missing components and run this script again.")

if __name__ == "__main__":
    main()