# Deep Learning Training Parameter Optimization Guide

## Overview
This document provides step-by-step instructions for optimizing deep learning model training parameters to reduce training time by 40-60% while maintaining model performance. Each step includes specific file locations and exact code changes.

## Implementation Priority
1. **Critical** (Immediate 30-50% speedup): Early stopping and epoch reduction
2. **High** (Additional 20-30% speedup): Batch size optimization
3. **Medium** (Performance stability): Learning rate scheduling

---

## Step 1: Enable Early Stopping for All Deep Learning Models

### 1.1 Update SimpleAutoencoder (models/simple_autoencoder.py)

**Location**: `models/simple_autoencoder.py`, line ~34
```python
# CURRENT CODE:
def __init__(self, n_features=4, sequence_len=64, encoding_dim=32,
             epochs=10, batch_size=64, learning_rate=0.01, device=None, use_mixed_precision=True):

# CHANGE TO:
def __init__(self, n_features=4, sequence_len=64, encoding_dim=32,
             epochs=30, batch_size=128, learning_rate=0.0005, device=None, use_mixed_precision=True,
             early_stopping_patience=10, min_delta=1e-4):
```

**Add early stopping tracking variables after line ~55**:
```python
self.early_stopping_patience = early_stopping_patience
self.min_delta = min_delta
self.best_loss = float('inf')
self.patience_counter = 0
```

**In the fit method, after line ~267 (inside epoch loop)**:
```python
# Add after calculating epoch_loss
avg_epoch_loss = epoch_loss / num_batches if num_batches > 0 else float('inf')

# Early stopping check
if avg_epoch_loss < self.best_loss - self.min_delta:
    self.best_loss = avg_epoch_loss
    self.patience_counter = 0
    # Save best model state
    self.best_model_state = self.model.state_dict().copy()
else:
    self.patience_counter += 1
    
if self.patience_counter >= self.early_stopping_patience:
    print(f"Early stopping triggered at epoch {epoch+1}")
    # Restore best model
    self.model.load_state_dict(self.best_model_state)
    break
```

### 1.2 Update SimpleUNet (models/simple_autoencoder.py)

**Location**: `models/simple_autoencoder.py`, line ~505
```python
# CURRENT CODE:
def __init__(self, n_features=4, sequence_len=64, epochs=50, batch_size=32, learning_rate=0.0001,
             device=None, use_mixed_precision=True):

# CHANGE TO:
def __init__(self, n_features=4, sequence_len=64, epochs=35, batch_size=128, learning_rate=0.0003,
             device=None, use_mixed_precision=True, early_stopping_patience=10, min_delta=1e-4):
```

**Apply same early stopping logic as SimpleAutoencoder**

---

## Step 2: Update Model Registry Default Parameters

### 2.1 Update ml_core.py Model Registry

**Location**: `ml_core.py`, lines 141-165

#### Autoencoder Configuration
```python
# Line ~149
'epochs': {'type': int, 'default': 30},  # Reduced from 50
# Line ~150
'batch_size': {'type': int, 'default': 128},  # Increased from 32
# Add new parameters:
'early_stopping_patience': {'type': int, 'default': 10},
'learning_rate': {'type': float, 'default': 0.0005},  # Increased from 0.0001
```

#### U-Net Configuration
```python
# Line ~161
'epochs': {'type': int, 'default': 35},  # Reduced from 50
# Line ~162
'batch_size': {'type': int, 'default': 128},  # Increased from 32
# Add new parameters:
'early_stopping_patience': {'type': int, 'default': 10},
'learning_rate': {'type': float, 'default': 0.0003},  # Increased from 0.0001
```

### 2.2 Update Advanced Model Configurations

**Location**: `ml_core.py`, lines 256-460

#### SAITS Model (line ~274)
```python
'epochs': {'type': int, 'default': 40, 'min': 10, 'max': 200},  # Reduced from 50
'batch_size': {'type': int, 'default': 64, 'min': 8, 'max': 256},  # Increased from 32
'learning_rate': {'type': float, 'default': 2e-3, 'min': 1e-5, 'max': 1e-2},  # Increased
# Add:
'early_stopping_patience': {'type': int, 'default': 12},
```

#### BRITS Model (line ~306)
```python
'epochs': {'type': int, 'default': 40, 'min': 10, 'max': 200},  # Reduced from 50
'batch_size': {'type': int, 'default': 256, 'min': 8, 'max': 512},  # Increased from 128
# Add:
'early_stopping_patience': {'type': int, 'default': 12},
```

#### Transformer Model (line ~370)
```python
'epochs': {'type': int, 'default': 50, 'min': 20, 'max': 200},  # Reduced from 100
'batch_size': {'type': int, 'default': 192, 'min': 8, 'max': 256},  # Increased from 96
'learning_rate': {'type': float, 'default': 2e-3, 'min': 1e-4, 'max': 1e-2},  # Increased
# Add:
'early_stopping_patience': {'type': int, 'default': 15},
```

---

## Step 3: Implement Learning Rate Scheduling

### 3.1 Add Cosine Annealing to SimpleAutoencoder

**Location**: `models/simple_autoencoder.py`, after line ~107
```python
# Replace current scheduler
# self.scheduler = torch.optim.lr_scheduler.StepLR(self.optimizer, step_size=5, gamma=0.8)

# With cosine annealing
self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
    self.optimizer, 
    T_max=epochs, 
    eta_min=learning_rate * 0.01
)
```

### 3.2 Add Warm-up for Transformer Models

**Location**: `models/advanced_models/transformer_model.py`, in fit method
```python
# Add warm-up scheduler wrapper
def get_linear_schedule_with_warmup(optimizer, num_warmup_steps, num_training_steps):
    def lr_lambda(current_step):
        if current_step < num_warmup_steps:
            return float(current_step) / float(max(1, num_warmup_steps))
        return max(0.0, float(num_training_steps - current_step) / 
                   float(max(1, num_training_steps - num_warmup_steps)))
    return torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)

# Calculate warmup steps (5% of total)
total_steps = epochs * len(dataloader)
warmup_steps = int(0.05 * total_steps)

# Create scheduler
scheduler = get_linear_schedule_with_warmup(
    self.optimizer, 
    num_warmup_steps=warmup_steps,
    num_training_steps=total_steps
)
```

---

## Step 4: Optimize Batch Processing

### 4.1 Dynamic Batch Size Adjustment

Create a new utility function in `utils/training_optimization.py`:
```python
def get_optimal_batch_size_for_model(model_type: str, n_features: int, 
                                   sequence_len: int, device: str) -> int:
    """
    Get optimal batch size based on model type and data dimensions.
    
    Args:
        model_type: Type of model ('autoencoder', 'unet', 'transformer', etc.)
        n_features: Number of features
        sequence_len: Sequence length
        device: Device type ('cuda' or 'cpu')
    
    Returns:
        Optimal batch size
    """
    if device == 'cpu':
        # Conservative for CPU
        base_sizes = {
            'autoencoder': 32,
            'unet': 32,
            'transformer': 16,
            'saits': 16,
            'brits': 64
        }
    else:
        # Optimized for GPU
        base_sizes = {
            'autoencoder': 128,
            'unet': 128,
            'transformer': 192,
            'saits': 64,
            'brits': 256
        }
    
    # Adjust based on sequence length and features
    memory_factor = (64 / sequence_len) * (4 / n_features)
    optimal_size = int(base_sizes.get(model_type, 64) * memory_factor)
    
    # Ensure it's a multiple of 8 for GPU efficiency
    if device == 'cuda':
        optimal_size = (optimal_size // 8) * 8
    
    return max(8, min(optimal_size, 512))
```

---

## Step 5: Model-Specific Architecture Optimizations

### 5.1 Transformer Optimization

**Location**: `ml_core.py`, Transformer configuration (line ~365)
```python
# Reduce model complexity for faster training
'd_model': {'type': int, 'default': 128, 'min': 64, 'max': 512},  # Reduced from 192
'n_heads': {'type': int, 'default': 4, 'min': 2, 'max': 16},  # Reduced from 6
'd_ff': {'type': int, 'default': 512, 'min': 256, 'max': 2048},  # Reduced from 768
```

### 5.2 SAITS Optimization

**Location**: `ml_core.py`, SAITS configuration (line ~272)
```python
'd_model': {'type': int, 'default': 192, 'min': 64, 'max': 512},  # Reduced from 256
```

---

## Step 6: Validation and Testing Strategy

### 6.1 Add Quick Validation Check

In each model's fit method, add validation every N epochs:
```python
# Validate every 5 epochs
if epoch % 5 == 0:
    self.model.eval()
    with torch.no_grad():
        val_loss = self._compute_validation_loss(val_data)
    self.model.train()
    print(f"Epoch {epoch}: Train Loss: {avg_epoch_loss:.4f}, Val Loss: {val_loss:.4f}")
```

---

## Step 7: Configuration Updates for Hyperparameter Tuning

### 7.1 Update configure_hyperparameters in config_handler.py

Add new parameters to the configuration:
```python
# Add to each model's hyperparameter configuration
'early_stopping': {
    'enabled': True,
    'patience': model_config.get('early_stopping_patience', 10),
    'min_delta': 1e-4
},
'scheduler': {
    'type': 'cosine',  # or 'step', 'exponential'
    'warmup_ratio': 0.05  # for transformer models
}
```

---

## Testing and Validation

### Test Script
Create `test_optimizations.py`:
```python
import time
from ml_core import MODEL_REGISTRY
from data_handler import load_las_files_from_directory

def benchmark_model(model_key, data, feats, tgt, original_epochs, optimized_epochs):
    """Benchmark training time before and after optimization."""
    
    # Original settings
    model_config = MODEL_REGISTRY[model_key].copy()
    model_config['hyperparameters']['epochs']['default'] = original_epochs
    model_config['hyperparameters']['batch_size']['default'] = 32
    
    start = time.time()
    # Run training with original settings
    # ... training code ...
    original_time = time.time() - start
    
    # Optimized settings
    model_config['hyperparameters']['epochs']['default'] = optimized_epochs
    model_config['hyperparameters']['batch_size']['default'] = 128
    
    start = time.time()
    # Run training with optimized settings
    # ... training code ...
    optimized_time = time.time() - start
    
    speedup = original_time / optimized_time
    print(f"{model_key}: {speedup:.2f}x speedup")
    
# Run benchmarks
benchmark_model('autoencoder', data, feats, tgt, 50, 30)
benchmark_model('transformer', data, feats, tgt, 100, 50)
```

---

## Expected Results

| Model | Original Time | Optimized Time | Speedup |
|-------|--------------|----------------|---------|
| Autoencoder | 50 epochs | 30 epochs + early stop | ~2.5x |
| U-Net | 50 epochs | 35 epochs + early stop | ~2.2x |
| Transformer | 100 epochs | 50 epochs + early stop | ~3.5x |
| SAITS | 50 epochs | 40 epochs + early stop | ~2.0x |
| BRITS | 50 epochs | 40 epochs + early stop | ~2.3x |

---

## Rollback Plan

If optimizations cause issues:
1. Keep original hyperparameter values in `_original` suffix
2. Add flag `use_optimized_params=True` to enable/disable
3. Monitor validation metrics closely
4. Revert batch sizes if OOM errors occur

---

## Monitoring Checklist

- [ ] Training loss convergence speed
- [ ] Validation loss stability
- [ ] GPU memory usage
- [ ] Training time per epoch
- [ ] Final model performance metrics
- [ ] Early stopping trigger frequency